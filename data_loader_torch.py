#! -*- coding:utf-8 -*-
import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
import json
from random import choice


BERT_MAX_LEN = 512
RANDOM_SEED = 2019

def find_head_idx(source, target):
    target_len = len(target)
    for i in range(len(source)):
        if source[i: i + target_len] == target:
            return i
    return -1

def to_tuple(sent):
    triple_list = []
    for triple in sent['triple_list']:
        triple_list.append(tuple(triple))
    sent['triple_list'] = triple_list

def load_data_torch(train_path, dev_path, test_path, rel_dict_path):
    """Load data for PyTorch training"""
    train_data = json.load(open(train_path))
    dev_data = json.load(open(dev_path))
    test_data = json.load(open(test_path))
    id2rel, rel2id = json.load(open(rel_dict_path))

    id2rel = {int(i): j for i, j in id2rel.items()}
    num_rels = len(id2rel)

    random_order = list(range(len(train_data)))
    np.random.seed(RANDOM_SEED)
    np.random.shuffle(random_order)
    train_data = [train_data[i] for i in random_order]

    for sent in train_data: 
        to_tuple(sent)
    for sent in dev_data:  
        to_tuple(sent)
    for sent in test_data: 
        to_tuple(sent)

    print("train_data len:", len(train_data))
    print("dev_data len:", len(dev_data))
    print("test_data len:", len(test_data))

    return train_data, dev_data, test_data, id2rel, rel2id, num_rels


class CasRelDataset(Dataset):
    def __init__(self, data, tokenizer, rel2id, num_rels, max_len):
        self.data = data
        self.tokenizer = tokenizer
        self.rel2id = rel2id
        self.num_rels = num_rels
        self.max_len = max_len
        
        # Pre-process data
        self.processed_data = []
        self._preprocess_data()
    
    def _preprocess_data(self):
        """Pre-process all data to avoid repeated processing during training"""
        for line in self.data:
            text = ' '.join(line['text'].split()[:self.max_len])
            
            # Tokenize text
            encoding = self.tokenizer(
                text,
                return_tensors='pt',
                max_length=BERT_MAX_LEN,
                truncation=True,
                padding=False,
                add_special_tokens=True
            )
            
            input_ids = encoding['input_ids'].squeeze(0)
            attention_mask = encoding['attention_mask'].squeeze(0)
            token_type_ids = encoding.get('token_type_ids', torch.zeros_like(input_ids))
            if token_type_ids is not None:
                token_type_ids = token_type_ids.squeeze(0)
            
            tokens = self.tokenizer.convert_ids_to_tokens(input_ids)
            text_len = len(tokens)
            
            # Build subject-relation-object mapping
            s2ro_map = {}
            for triple in line['triple_list']:
                # Tokenize entities
                subj_tokens = self.tokenizer.tokenize(triple[0])
                obj_tokens = self.tokenizer.tokenize(triple[2])
                
                # Find positions in the tokenized text
                sub_head_idx = find_head_idx(tokens, subj_tokens)
                obj_head_idx = find_head_idx(tokens, obj_tokens)
                
                if sub_head_idx != -1 and obj_head_idx != -1:
                    sub = (sub_head_idx, sub_head_idx + len(subj_tokens) - 1)
                    if sub not in s2ro_map:
                        s2ro_map[sub] = []
                    s2ro_map[sub].append((
                        obj_head_idx,
                        obj_head_idx + len(obj_tokens) - 1,
                        self.rel2id[triple[1]]
                    ))
            
            if s2ro_map:
                # Create labels
                sub_heads = torch.zeros(text_len)
                sub_tails = torch.zeros(text_len)
                
                for s in s2ro_map:
                    sub_heads[s[0]] = 1
                    sub_tails[s[1]] = 1
                
                # Randomly select a subject for object labeling
                sub_head, sub_tail = choice(list(s2ro_map.keys()))
                obj_heads = torch.zeros(text_len, self.num_rels)
                obj_tails = torch.zeros(text_len, self.num_rels)
                
                for ro in s2ro_map.get((sub_head, sub_tail), []):
                    obj_heads[ro[0]][ro[2]] = 1
                    obj_tails[ro[1]][ro[2]] = 1
                
                self.processed_data.append({
                    'input_ids': input_ids,
                    'attention_mask': attention_mask,
                    'token_type_ids': token_type_ids,
                    'sub_heads': sub_heads,
                    'sub_tails': sub_tails,
                    'sub_head_idx': sub_head,
                    'sub_tail_idx': sub_tail,
                    'obj_heads': obj_heads,
                    'obj_tails': obj_tails
                })
    
    def __len__(self):
        return len(self.processed_data)
    
    def __getitem__(self, idx):
        return self.processed_data[idx]


def collate_fn(batch):
    """Custom collate function for DataLoader"""
    # Get maximum sequence length in the batch
    max_len = max([item['input_ids'].size(0) for item in batch])
    
    # Pad all sequences to max_len
    input_ids = []
    attention_masks = []
    token_type_ids = []
    sub_heads = []
    sub_tails = []
    sub_head_indices = []
    sub_tail_indices = []
    obj_heads = []
    obj_tails = []
    
    for item in batch:
        seq_len = item['input_ids'].size(0)
        pad_len = max_len - seq_len
        
        # Pad input_ids and attention_mask
        input_ids.append(torch.cat([
            item['input_ids'], 
            torch.zeros(pad_len, dtype=torch.long)
        ]))
        
        attention_masks.append(torch.cat([
            item['attention_mask'], 
            torch.zeros(pad_len, dtype=torch.long)
        ]))
        
        token_type_ids.append(torch.cat([
            item['token_type_ids'], 
            torch.zeros(pad_len, dtype=torch.long)
        ]))
        
        # Pad labels
        sub_heads.append(torch.cat([
            item['sub_heads'], 
            torch.zeros(pad_len)
        ]))
        
        sub_tails.append(torch.cat([
            item['sub_tails'], 
            torch.zeros(pad_len)
        ]))
        
        obj_heads.append(torch.cat([
            item['obj_heads'], 
            torch.zeros(pad_len, item['obj_heads'].size(1))
        ]))
        
        obj_tails.append(torch.cat([
            item['obj_tails'], 
            torch.zeros(pad_len, item['obj_tails'].size(1))
        ]))
        
        sub_head_indices.append(item['sub_head_idx'])
        sub_tail_indices.append(item['sub_tail_idx'])
    
    return {
        'input_ids': torch.stack(input_ids),
        'attention_mask': torch.stack(attention_masks),
        'token_type_ids': torch.stack(token_type_ids),
        'sub_heads': torch.stack(sub_heads),
        'sub_tails': torch.stack(sub_tails),
        'sub_head_indices': torch.tensor(sub_head_indices).unsqueeze(1),
        'sub_tail_indices': torch.tensor(sub_tail_indices).unsqueeze(1),
        'obj_heads': torch.stack(obj_heads),
        'obj_tails': torch.stack(obj_tails)
    }


def create_data_loader(data, tokenizer, rel2id, num_rels, max_len, batch_size, shuffle=True):
    """Create PyTorch DataLoader"""
    dataset = CasRelDataset(data, tokenizer, rel2id, num_rels, max_len)
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        collate_fn=collate_fn,
        num_workers=0  # Set to 0 for Windows compatibility
    )
