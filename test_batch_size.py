#! -*- coding:utf-8 -*-
"""
Test script to verify batch_size is working correctly
"""
import sys
import argparse

def test_argparse():
    """Test if argparse is working correctly"""
    print("=== Testing Batch Size Configuration ===\n")
    
    # Simulate the same argument parser as run_torch.py
    parser = argparse.ArgumentParser(description='CasRel PyTorch Model Controller')
    parser.add_argument('--train', default=False, type=bool, help='to train the CasRel model')
    parser.add_argument('--dataset', default='NYT', type=str, help='specify the dataset')
    parser.add_argument('--gpu', default='0', type=str, help='GPU device to use')
    parser.add_argument('--batch_size', default=64, type=int, help='batch size for training')
    parser.add_argument('--epochs', default=100, type=int, help='number of training epochs')
    parser.add_argument('--lr', default=1e-5, type=float, help='learning rate')
    parser.add_argument('--max_len', default=100, type=int, help='maximum sequence length')
    parser.add_argument('--patience', default=7, type=int, help='early stopping patience')
    parser.add_argument('--use_span_average', default=False, type=bool, help='use span average for subject features')
    
    # Parse arguments
    args = parser.parse_args()
    
    print(f"Command line arguments: {sys.argv}")
    print(f"Parsed batch_size: {args.batch_size}")
    print(f"Parsed train: {args.train}")
    print(f"Parsed dataset: {args.dataset}")
    
    # Check if batch_size was explicitly set
    if '--batch_size' in sys.argv:
        batch_idx = sys.argv.index('--batch_size')
        if batch_idx + 1 < len(sys.argv):
            explicit_batch_size = sys.argv[batch_idx + 1]
            print(f"Explicitly set batch_size: {explicit_batch_size}")
        else:
            print("--batch_size flag found but no value provided")
    else:
        print("Using default batch_size: 64")
    
    return args

def test_data_loader():
    """Test if data loader respects batch_size"""
    try:
        from data_loader_torch import create_data_loader, load_data_torch
        from transformers import BertTokenizer
        
        print(f"\n=== Testing Data Loader ===")
        
        # Load small amount of data
        dataset = 'NYT'
        train_path = f'data/{dataset}/train_triples.json'
        dev_path = f'data/{dataset}/dev_triples.json'
        test_path = f'data/{dataset}/test_triples.json'
        rel_dict_path = f'data/{dataset}/rel2id.json'
        
        train_data, dev_data, test_data, id2rel, rel2id, num_rels = load_data_torch(
            train_path, dev_path, test_path, rel_dict_path
        )
        
        # Use small subset for testing
        small_data = train_data[:100]  # Only 100 samples
        
        tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
        
        # Test different batch sizes
        for test_batch_size in [8, 16, 32, 64]:
            print(f"\nTesting batch_size: {test_batch_size}")
            
            try:
                train_loader = create_data_loader(
                    small_data, tokenizer, rel2id, num_rels, 
                    100, test_batch_size, shuffle=False  # max_len=100
                )
                
                print(f"  DataLoader created successfully")
                print(f"  Number of batches: {len(train_loader)}")
                print(f"  Expected batches: {(len(small_data) + test_batch_size - 1) // test_batch_size}")
                
                # Check first batch
                for i, batch in enumerate(train_loader):
                    actual_batch_size = batch['input_ids'].size(0)
                    print(f"  Batch {i+1} size: {actual_batch_size}")
                    if i == 0:  # Only check first batch
                        break
                        
            except Exception as e:
                print(f"  Error with batch_size {test_batch_size}: {e}")
                
    except ImportError as e:
        print(f"Cannot test data loader: {e}")
        print("Make sure PyTorch and transformers are installed")

def main():
    """Main test function"""
    args = test_argparse()
    
    # Test if we can create data loader with the parsed batch_size
    if args.batch_size != 64:
        print(f"\n⚠️  WARNING: batch_size is {args.batch_size}, not the expected default 64")
        print("This suggests the default was overridden by command line arguments")
    else:
        print(f"\n✅ batch_size is correctly set to 64")
    
    test_data_loader()
    
    print(f"\n=== Troubleshooting Tips ===")
    print("1. Make sure you're not passing --batch_size in command line")
    print("2. Check if there are memory limitations causing automatic reduction")
    print("3. Verify the DataLoader is actually using the specified batch_size")
    print("4. Run with: python run_torch.py --train=True (no explicit batch_size)")

if __name__ == '__main__':
    main()
