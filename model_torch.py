#! -*- coding:utf-8 -*-
import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import BertModel, BertConfig
import numpy as np
from tqdm import tqdm
from utils_torch import extract_items_torch, metric_torch


class CasRelModel(nn.Module):
    def __init__(self, bert_model_path, num_rels, dropout_rate=0.1):
        super(CasRelModel, self).__init__()
        self.num_rels = num_rels

        # Load BERT model
        self.bert = BertModel.from_pretrained(bert_model_path)
        self.hidden_size = self.bert.config.hidden_size

        # Subject extraction layers
        self.sub_heads_linear = nn.Linear(self.hidden_size, 1)
        self.sub_tails_linear = nn.Linear(self.hidden_size, 1)

        # Object extraction layers
        self.obj_heads_linear = nn.Linear(self.hidden_size, num_rels)
        self.obj_tails_linear = nn.Linear(self.hidden_size, num_rels)

        self.dropout = nn.Dropout(dropout_rate)

    def forward(self, input_ids, attention_mask, token_type_ids=None,
                sub_head_idx=None, sub_tail_idx=None):
        """
        Forward pass of the model

        Args:
            input_ids: [batch_size, seq_len]
            attention_mask: [batch_size, seq_len]
            token_type_ids: [batch_size, seq_len]
            sub_head_idx: [batch_size, 1] - for object extraction
            sub_tail_idx: [batch_size, 1] - for object extraction
        """
        # Get BERT outputs
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids
        )
        sequence_output = outputs.last_hidden_state  # [batch_size, seq_len, hidden_size]
        sequence_output = self.dropout(sequence_output)

        # Subject extraction
        sub_heads_logits = self.sub_heads_linear(sequence_output).squeeze(-1)  # [batch_size, seq_len]
        sub_tails_logits = self.sub_tails_linear(sequence_output).squeeze(-1)  # [batch_size, seq_len]

        # Apply sigmoid for subject prediction
        sub_heads_pred = torch.sigmoid(sub_heads_logits)
        sub_tails_pred = torch.sigmoid(sub_tails_logits)

        # Object extraction (only when subject positions are provided)
        if sub_head_idx is not None and sub_tail_idx is not None:
            # Get subject representations
            batch_size, seq_len, hidden_size = sequence_output.size()

            # Gather subject head and tail representations
            sub_head_features = self.gather_features(sequence_output, sub_head_idx)  # [batch_size, hidden_size]
            sub_tail_features = self.gather_features(sequence_output, sub_tail_idx)  # [batch_size, hidden_size]

            # Average subject representation
            sub_features = (sub_head_features + sub_tail_features) / 2  # [batch_size, hidden_size]

            # Add subject information to all positions
            sub_features_expanded = sub_features.unsqueeze(1).expand(-1, seq_len, -1)  # [batch_size, seq_len, hidden_size]
            enhanced_sequence_output = sequence_output + sub_features_expanded

            # Object extraction
            obj_heads_logits = self.obj_heads_linear(enhanced_sequence_output)  # [batch_size, seq_len, num_rels]
            obj_tails_logits = self.obj_tails_linear(enhanced_sequence_output)  # [batch_size, seq_len, num_rels]

            # Apply sigmoid for object prediction
            obj_heads_pred = torch.sigmoid(obj_heads_logits)
            obj_tails_pred = torch.sigmoid(obj_tails_logits)

            return sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred
        else:
            return sub_heads_pred, sub_tails_pred

    def gather_features(self, sequence_output, indices):
        """
        Gather features from sequence_output based on indices

        Args:
            sequence_output: [batch_size, seq_len, hidden_size]
            indices: [batch_size, 1]

        Returns:
            gathered_features: [batch_size, hidden_size]
        """
        batch_size, seq_len, hidden_size = sequence_output.size()
        batch_indices = torch.arange(batch_size, device=sequence_output.device).unsqueeze(1)  # [batch_size, 1]

        # Combine batch indices with position indices
        gather_indices = torch.cat([batch_indices, indices], dim=1)  # [batch_size, 2]

        # Use advanced indexing to gather features
        gathered_features = sequence_output[gather_indices[:, 0], gather_indices[:, 1]]  # [batch_size, hidden_size]

        return gathered_features


class CasRelLoss(nn.Module):
    def __init__(self):
        super(CasRelLoss, self).__init__()
        self.bce_loss = nn.BCELoss(reduction='none')

    def forward(self, sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred,
                sub_heads_gold, sub_tails_gold, obj_heads_gold, obj_tails_gold, mask):
        """
        Calculate the total loss for CasRel model

        Args:
            sub_heads_pred, sub_tails_pred: [batch_size, seq_len]
            obj_heads_pred, obj_tails_pred: [batch_size, seq_len, num_rels]
            sub_heads_gold, sub_tails_gold: [batch_size, seq_len]
            obj_heads_gold, obj_tails_gold: [batch_size, seq_len, num_rels]
            mask: [batch_size, seq_len]
        """
        # Subject losses
        sub_heads_loss = self.bce_loss(sub_heads_pred, sub_heads_gold)
        sub_heads_loss = torch.sum(sub_heads_loss * mask) / torch.sum(mask)

        sub_tails_loss = self.bce_loss(sub_tails_pred, sub_tails_gold)
        sub_tails_loss = torch.sum(sub_tails_loss * mask) / torch.sum(mask)

        # Object losses
        obj_heads_loss = self.bce_loss(obj_heads_pred, obj_heads_gold)
        obj_heads_loss = torch.sum(obj_heads_loss, dim=2)  # Sum over relations
        obj_heads_loss = torch.sum(obj_heads_loss * mask) / torch.sum(mask)

        obj_tails_loss = self.bce_loss(obj_tails_pred, obj_tails_gold)
        obj_tails_loss = torch.sum(obj_tails_loss, dim=2)  # Sum over relations
        obj_tails_loss = torch.sum(obj_tails_loss * mask) / torch.sum(mask)

        # Total loss
        total_loss = (sub_heads_loss + sub_tails_loss) + (obj_heads_loss + obj_tails_loss)

        return total_loss, sub_heads_loss, sub_tails_loss, obj_heads_loss, obj_tails_loss


class EarlyStopping:
    def __init__(self, patience=7, min_delta=1e-4, save_path='best_model.pt'):
        self.patience = patience
        self.min_delta = min_delta
        self.save_path = save_path
        self.best_score = -np.inf
        self.wait = 0
        self.stopped_epoch = 0

    def __call__(self, score, model, epoch):
        if score > self.best_score + self.min_delta:
            self.best_score = score
            self.wait = 0
            torch.save(model.state_dict(), self.save_path)
            return False
        else:
            self.wait += 1
            if self.wait >= self.patience:
                self.stopped_epoch = epoch
                return True
            return False


def evaluate_model(model, eval_data, tokenizer, id2rel, device, exact_match=False):
    """
    Evaluate the model on evaluation data
    """
    model.eval()
    with torch.no_grad():
        precision, recall, f1 = metric_torch(model, eval_data, id2rel, tokenizer, device, exact_match)
    return precision, recall, f1