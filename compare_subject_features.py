#! -*- coding:utf-8 -*-
"""
<PERSON>rip<PERSON> to compare different subject feature extraction methods
"""
import torch
import numpy as np
from transformers import BertTokenizer

def demonstrate_subject_features():
    """Demonstrate the difference between span average and head+tail average"""
    
    print("=== Subject Feature Extraction Comparison ===\n")
    
    # Create a simple example
    tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
    text = "Apple Inc. is a technology company"
    
    # Tokenize
    encoding = tokenizer(text, return_tensors='pt')
    tokens = tokenizer.convert_ids_to_tokens(encoding['input_ids'][0])
    
    print(f"Original text: {text}")
    print(f"Tokens: {tokens}")
    print()
    
    # Simulate BERT features (random for demonstration)
    seq_len = len(tokens)
    hidden_size = 768
    torch.manual_seed(42)  # For reproducible results
    sequence_output = torch.randn(1, seq_len, hidden_size)
    
    # Find "Apple Inc." span (assuming it's tokens 1-3)
    # [CLS] Apple Inc . is a technology company [SEP]
    #   0    1   2  3  4  5     6        7      8
    
    subject_text = "Apple Inc."
    sub_head_idx = 1  # "Apple"
    sub_tail_idx = 2  # "Inc"
    
    print(f"Subject: '{subject_text}'")
    print(f"Subject head position: {sub_head_idx} ('{tokens[sub_head_idx]}')")
    print(f"Subject tail position: {sub_tail_idx} ('{tokens[sub_tail_idx]}')")
    print()
    
    # Method 1: Head + Tail Average (Original Implementation)
    sub_head_features = sequence_output[0, sub_head_idx, :]  # [hidden_size]
    sub_tail_features = sequence_output[0, sub_tail_idx, :]  # [hidden_size]
    head_tail_average = (sub_head_features + sub_tail_features) / 2
    
    print("Method 1: Head + Tail Average (Original)")
    print(f"  - Uses features from positions {sub_head_idx} and {sub_tail_idx}")
    print(f"  - Feature vector shape: {head_tail_average.shape}")
    print(f"  - Feature vector norm: {head_tail_average.norm().item():.4f}")
    print()
    
    # Method 2: Span Average (Improved Implementation)
    span_features = sequence_output[0, sub_head_idx:sub_tail_idx+1, :].mean(dim=0)  # [hidden_size]
    
    print("Method 2: Span Average (Improved)")
    print(f"  - Uses features from positions {sub_head_idx} to {sub_tail_idx} (inclusive)")
    print(f"  - Includes {sub_tail_idx - sub_head_idx + 1} token(s)")
    print(f"  - Feature vector shape: {span_features.shape}")
    print(f"  - Feature vector norm: {span_features.norm().item():.4f}")
    print()
    
    # Compare the methods
    cosine_similarity = torch.cosine_similarity(head_tail_average, span_features, dim=0)
    l2_distance = torch.norm(head_tail_average - span_features).item()
    
    print("Comparison:")
    print(f"  - Cosine similarity: {cosine_similarity.item():.4f}")
    print(f"  - L2 distance: {l2_distance:.4f}")
    print()
    
    # Show why span average might be better
    print("Why Span Average Might Be Better:")
    print("1. Semantic completeness: Captures the entire entity span")
    print("2. Robustness: Less sensitive to tokenization artifacts")
    print("3. Information richness: Uses all available context within the entity")
    print("4. Linguistic intuition: Entities are spans, not just boundary points")
    print()
    
    # Example with longer entity
    print("=== Example with Longer Entity ===")
    text2 = "The United States of America is a country"
    encoding2 = tokenizer(text2, return_tensors='pt')
    tokens2 = tokenizer.convert_ids_to_tokens(encoding2['input_ids'][0])
    
    print(f"Text: {text2}")
    print(f"Tokens: {tokens2}")
    
    # "United States of America" might be tokens 1-5
    # [CLS] The United States of America is a country [SEP]
    #   0   1    2      3     4   5       6  7    8      9
    
    long_sub_head = 2  # "United"
    long_sub_tail = 5  # "America"
    
    print(f"Entity span: tokens {long_sub_head}-{long_sub_tail}")
    print(f"Head+Tail method: Only uses '{tokens2[long_sub_head]}' and '{tokens2[long_sub_tail]}'")
    print(f"Span method: Uses all tokens: {tokens2[long_sub_head:long_sub_tail+1]}")
    print()
    
    print("Conclusion:")
    print("For multi-token entities, span average provides richer representation")
    print("by incorporating all constituent tokens, not just the boundaries.")

if __name__ == '__main__':
    try:
        demonstrate_subject_features()
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have transformers installed: pip install transformers")
