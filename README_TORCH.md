# CasRel PyTorch Implementation

This directory contains a PyTorch implementation of the CasRel (Cascade Relation Extraction) model, converted from the original Keras/TensorFlow implementation.

## Files Overview

### Core Implementation
- **`model_torch.py`** - PyTorch implementation of the CasRel model
- **`run_torch.py`** - Training and evaluation script for PyTorch version
- **`data_loader_torch.py`** - PyTorch DataLoader and Dataset classes
- **`utils_torch.py`** - Utility functions for PyTorch implementation
- **`test_torch.py`** - Test script to verify the implementation

### Original Files (for reference)
- **`model.py`** - Original Keras implementation
- **`run.py`** - Original training script
- **`data_loader.py`** - Original data loading utilities
- **`utils.py`** - Original utility functions

## Requirements

```bash
pip install torch transformers numpy tqdm
```

## Quick Start

### 1. Prepare Data
First, make sure you have prepared the data using the original data preparation scripts:

```bash
# Generate processed data from raw NYT data
cd data/NYT/raw_NYT
python generate.py

# Build training data
cd ..
python build_data.py
```

### 2. Test Installation
Run the test script to verify everything is working:

```bash
python test_torch.py
```

### 3. Train Model
Train the PyTorch model:

```bash
python run_torch.py --train=True --dataset=NYT --batch_size=6 --epochs=100 --lr=1e-5
```

### 4. Evaluate Model
Evaluate the trained model:

```bash
python run_torch.py --train=False --dataset=NYT
```

## Command Line Arguments

### `run_torch.py` Arguments:
- `--train`: Whether to train the model (default: False)
- `--dataset`: Dataset to use (default: 'NYT')
- `--gpu`: GPU device to use (default: '0')
- `--batch_size`: Batch size for training (default: 6)
- `--epochs`: Number of training epochs (default: 100)
- `--lr`: Learning rate (default: 1e-5)
- `--max_len`: Maximum sequence length (default: 100)
- `--patience`: Early stopping patience (default: 7)
- `--use_span_average`: Use span average for subject features (default: True)

## Model Architecture

The PyTorch implementation maintains the same architecture as the original:

1. **BERT Encoder**: Pre-trained BERT model for token representations
2. **Subject Extraction**: Two linear layers to predict subject head and tail positions
3. **Object Extraction**: Two linear layers to predict object head and tail positions for each relation type
4. **Cascade Structure**: Subject information is used to enhance object extraction

## Subject Feature Extraction Methods

This PyTorch implementation offers two methods for extracting subject features:

### 1. Span Average (Recommended, Default)
```bash
python run_torch.py --use_span_average=True
```
- **Method**: Averages BERT features across the entire subject span
- **Example**: For "Apple Inc." (tokens 1-2), uses average of features[1:3]
- **Advantages**:
  - More semantically meaningful representation
  - Captures complete entity information
  - Better for multi-token entities
  - More robust to tokenization variations

### 2. Head+Tail Average (Original)
```bash
python run_torch.py --use_span_average=False
```
- **Method**: Averages only the head and tail position features
- **Example**: For "Apple Inc." (tokens 1-2), uses (features[1] + features[2]) / 2
- **Advantages**:
  - Matches original paper implementation
  - Computationally simpler
  - Proven effectiveness in original work

### Comparison
Run the comparison script to see the difference:
```bash
python compare_subject_features.py
```

## Key Differences from Original

### Model Implementation (`model_torch.py`)
- Uses `torch.nn.Module` instead of Keras layers
- **Improved Subject Feature Extraction**: Offers two methods:
  - **Span Average** (default): Averages features across the entire subject span
  - **Head+Tail Average**: Original method averaging only head and tail positions
- Implements custom `gather_features` method for position-based feature extraction
- Uses PyTorch's `nn.BCELoss` for loss calculation

### Data Loading (`data_loader_torch.py`)
- Uses PyTorch's `Dataset` and `DataLoader` classes
- Implements custom `collate_fn` for batch padding
- Pre-processes data during dataset initialization for efficiency

### Training Loop (`run_torch.py`)
- Manual training loop with explicit forward/backward passes
- Integrated early stopping and model evaluation
- Uses `tqdm` for progress tracking

### Utilities (`utils_torch.py`)
- Adapted extraction and evaluation functions for PyTorch tensors
- Uses Transformers library tokenizer instead of keras-bert

## Performance

The PyTorch implementation should achieve similar performance to the original Keras implementation:

- **NYT Dataset**: ~90% F1 score
- **Training Time**: Similar to original (depends on hardware)
- **Memory Usage**: Comparable to original implementation

## Troubleshooting

### Common Issues:

1. **CUDA Out of Memory**
   - Reduce batch size: `--batch_size=4` or `--batch_size=2`
   - Reduce max sequence length: `--max_len=80`

2. **Slow Training**
   - Ensure CUDA is available and being used
   - Check GPU utilization with `nvidia-smi`

3. **Import Errors**
   - Install required packages: `pip install torch transformers`
   - Run test script: `python test_torch.py`

4. **Data Loading Issues**
   - Ensure data files exist in `data/NYT/`
   - Run data preparation scripts first

### Debugging:
Run the test script to identify issues:
```bash
python test_torch.py
```

## File Structure
```
.
├── model_torch.py          # PyTorch model implementation
├── run_torch.py           # Training/evaluation script
├── data_loader_torch.py   # Data loading utilities
├── utils_torch.py         # Utility functions
├── test_torch.py          # Test script
├── README_TORCH.md        # This file
├── data/
│   └── NYT/
│       ├── train_triples.json
│       ├── dev_triples.json
│       ├── test_triples.json
│       └── rel2id.json
├── saved_weights/
│   └── NYT/
│       └── best_model_torch.pt
└── results/
    └── NYT/
        └── test_result_torch.json
```

## Contributing

When making changes to the PyTorch implementation:

1. Ensure compatibility with the original model architecture
2. Test changes using `test_torch.py`
3. Verify performance on NYT dataset
4. Update documentation as needed

## License

Same as the original CasRel implementation.
