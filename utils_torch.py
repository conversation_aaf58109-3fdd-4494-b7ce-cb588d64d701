#! -*- coding:utf-8 -*-
import torch
import numpy as np
import codecs
from tqdm import tqdm
import json

from transformers import BertTokenizer

BERT_MAX_LEN = 512

def get_tokenizer_torch(model_path):
    """Get PyTorch BERT tokenizer"""
    return BertTokenizer.from_pretrained(model_path, do_lower_case=False)

def extract_items_torch(model, tokenizer, text_in, id2rel, device, h_bar=0.5, t_bar=0.5):
    """
    Extract relation triples from text using PyTorch model
    """
    model.eval()
    
    # Tokenize input
    encoding = tokenizer(
        text_in,
        return_tensors='pt',
        max_length=BERT_MAX_LEN,
        truncation=True,
        padding=True
    )
    
    input_ids = encoding['input_ids'].to(device)
    attention_mask = encoding['attention_mask'].to(device)
    token_type_ids = encoding.get('token_type_ids', None)
    if token_type_ids is not None:
        token_type_ids = token_type_ids.to(device)
    
    with torch.no_grad():
        # Subject extraction
        sub_heads_pred, sub_tails_pred = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids
        )
        
        # Find subject positions
        sub_heads = torch.where(sub_heads_pred[0] > h_bar)[0].cpu().numpy()
        sub_tails = torch.where(sub_tails_pred[0] > t_bar)[0].cpu().numpy()
        
        subjects = []
        tokens = tokenizer.convert_ids_to_tokens(input_ids[0])
        
        for sub_head in sub_heads:
            sub_tail_candidates = sub_tails[sub_tails >= sub_head]
            if len(sub_tail_candidates) > 0:
                sub_tail = sub_tail_candidates[0]
                subject_tokens = tokens[sub_head: sub_tail + 1]
                subjects.append((subject_tokens, sub_head, sub_tail))
        
        if not subjects:
            return []
        
        # Object extraction for each subject
        triple_list = []
        for subject_tokens, sub_head, sub_tail in subjects:
            # Prepare inputs for object extraction
            sub_head_idx = torch.tensor([[sub_head]], device=device)
            sub_tail_idx = torch.tensor([[sub_tail]], device=device)
            
            # Object extraction
            _, _, obj_heads_pred, obj_tails_pred = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                token_type_ids=token_type_ids,
                sub_head_idx=sub_head_idx,
                sub_tail_idx=sub_tail_idx
            )
            
            # Find object positions
            obj_heads_indices = torch.where(obj_heads_pred[0] > h_bar)
            obj_tails_indices = torch.where(obj_tails_pred[0] > t_bar)
            
            obj_heads_pos = obj_heads_indices[0].cpu().numpy()
            obj_heads_rel = obj_heads_indices[1].cpu().numpy()
            obj_tails_pos = obj_tails_indices[0].cpu().numpy()
            obj_tails_rel = obj_tails_indices[1].cpu().numpy()
            
            # Match object heads and tails
            for obj_head, rel_head in zip(obj_heads_pos, obj_heads_rel):
                for obj_tail, rel_tail in zip(obj_tails_pos, obj_tails_rel):
                    if obj_head <= obj_tail and rel_head == rel_tail:
                        rel = id2rel[rel_head]
                        obj_tokens = tokens[obj_head: obj_tail + 1]
                        
                        # Clean up tokens
                        sub_text = ''.join([token.lstrip("##") for token in subject_tokens])
                        sub_text = ' '.join(sub_text.split('[unused1]'))
                        
                        obj_text = ''.join([token.lstrip("##") for token in obj_tokens])
                        obj_text = ' '.join(obj_text.split('[unused1]'))
                        
                        triple_list.append((sub_text, rel, obj_text))
                        break
        
        # Remove duplicates
        triple_set = set(triple_list)
        return list(triple_set)

def partial_match_torch(pred_set, gold_set):
    """Partial matching for evaluation"""
    pred = {(i[0].split(' ')[0] if len(i[0].split(' ')) > 0 else i[0], i[1], 
             i[2].split(' ')[0] if len(i[2].split(' ')) > 0 else i[2]) for i in pred_set}
    gold = {(i[0].split(' ')[0] if len(i[0].split(' ')) > 0 else i[0], i[1], 
             i[2].split(' ')[0] if len(i[2].split(' ')) > 0 else i[2]) for i in gold_set}
    return pred, gold

def metric_torch(model, eval_data, id2rel, tokenizer, device, exact_match=False, output_path=None):
    """
    Evaluate model performance using PyTorch
    """
    if output_path:
        F = open(output_path, 'w')
    
    orders = ['subject', 'relation', 'object']
    correct_num, predict_num, gold_num = 1e-10, 1e-10, 1e-10
    
    for line in tqdm(iter(eval_data)):
        pred_triples = set(extract_items_torch(model, tokenizer, line['text'], id2rel, device))
        gold_triples = set(line['triple_list'])
        
        if not exact_match:
            pred_triples_eval, gold_triples_eval = partial_match_torch(pred_triples, gold_triples)
        else:
            pred_triples_eval, gold_triples_eval = pred_triples, gold_triples
        
        correct_num += len(pred_triples_eval & gold_triples_eval)
        predict_num += len(pred_triples_eval)
        gold_num += len(gold_triples_eval)
        
        if output_path:
            result = json.dumps({
                'text': line['text'],
                'triple_list_gold': [
                    dict(zip(orders, triple)) for triple in gold_triples
                ],
                'triple_list_pred': [
                    dict(zip(orders, triple)) for triple in pred_triples
                ],
                'new': [
                    dict(zip(orders, triple)) for triple in pred_triples - gold_triples
                ],
                'lack': [
                    dict(zip(orders, triple)) for triple in gold_triples - pred_triples
                ]
            }, ensure_ascii=False, indent=4)
            F.write(result + '\n')
    
    if output_path:
        F.close()
    
    precision = correct_num / predict_num
    recall = correct_num / gold_num
    f1_score = 2 * precision * recall / (precision + recall)
    
    print(f'correct_num:{correct_num}\npredict_num:{predict_num}\ngold_num:{gold_num}')
    return precision, recall, f1_score

def seq_padding_torch(batch, padding=0):
    """Padding sequences to the same length"""
    length_batch = [len(seq) for seq in batch]
    max_length = max(length_batch)
    return torch.tensor([
        seq + [padding] * (max_length - len(seq)) if len(seq) < max_length else seq 
        for seq in batch
    ])
