# CasRel PyTorch Implementation - Recall Issues and Fixes

## 🔍 Identified Issues

### 1. **Critical: Early Break in Object Extraction** ✅ FIXED
**Problem**: In `utils_torch.py`, there was a `break` statement that caused each subject to only extract the first matching object, severely limiting recall.

**Location**: `utils_torch.py:102`
```python
# WRONG (old code)
triple_list.append((sub_text, rel, obj_text))
break  # ❌ This breaks after first object!

# FIXED (new code)  
triple_list.append((sub_text, rel, obj_text))
# ✅ No break - extract all objects for each subject
```

### 2. **Subject Feature Method** ✅ FIXED
**Problem**: Default was set to span average instead of original head+tail average method.

**Location**: `run_torch.py:31`
```python
# CHANGED: Default back to original method
parser.add_argument('--use_span_average', default=False, type=bool, ...)
```

### 3. **Potential: Tokenization Differences**
**Issue**: PyTorch uses Transformers library tokenizer vs original keras-bert tokenizer.
**Status**: Needs verification - different tokenization could affect entity boundaries.

### 4. **Potential: Batch Processing**
**Issue**: Original implementation processes all subjects in batch, PyTorch version processes individually.
**Status**: Performance difference, but shouldn't affect recall significantly.

## 🛠️ Applied Fixes

### Fix 1: Remove Early Break (Critical)
```python
# File: utils_torch.py
# Removed the break statement that was limiting object extraction
for obj_head, rel_head in zip(obj_heads_pos, obj_heads_rel):
    for obj_tail, rel_tail in zip(obj_tails_pos, obj_tails_rel):
        if obj_head <= obj_tail and rel_head == rel_tail:
            rel = id2rel[rel_head]
            obj_tokens = tokens[obj_head: obj_tail + 1]
            # ... process triple ...
            triple_list.append((sub_text, rel, obj_text))
            # ✅ NO BREAK HERE - continue extracting all objects
```

### Fix 2: Use Original Subject Feature Method
```python
# File: run_torch.py  
# Default to head+tail average (original method)
--use_span_average=False  # Default changed from True to False
```

### Fix 3: Consistent Entity Boundary Handling
```python
# Both subject and object extraction use inclusive ranges
subject_tokens = tokens[sub_head: sub_tail + 1]  # +1 for inclusive
obj_tokens = tokens[obj_head: obj_tail + 1]      # +1 for inclusive
```

## 🧪 Testing and Debugging

### Debug Script
Run the debug script to analyze extraction issues:
```bash
python debug_recall.py
```

This will:
- Test extraction on sample data
- Show subject/object predictions at different thresholds
- Compare predicted vs gold triples
- Identify missed extractions

### Recommended Testing Steps

1. **Test with Fixed Implementation**:
   ```bash
   python run_torch.py --train=False --dataset=NYT --use_span_average=False
   ```

2. **Compare Thresholds**:
   Try different threshold values if recall is still low:
   ```python
   # In extract_items_torch function, try:
   h_bar = 0.3  # Lower threshold for heads
   t_bar = 0.3  # Lower threshold for tails
   ```

3. **Verify Model Training**:
   Ensure the model was properly trained:
   ```bash
   python run_torch.py --train=True --dataset=NYT --epochs=5 --batch_size=4
   ```

## 🎯 Expected Results After Fixes

The **early break fix** should significantly improve recall because:
- **Before**: Each subject could only extract 1 object (first match)
- **After**: Each subject can extract all matching objects

For a subject with 3 gold objects:
- **Before Fix**: Recall ≤ 33% (only 1/3 objects extracted)
- **After Fix**: Recall up to 100% (all 3 objects can be extracted)

## 🔄 Additional Recommendations

### 1. Threshold Tuning
If recall is still low, try lower thresholds:
```python
# In utils_torch.py, extract_items_torch function
pred_triples = extract_items_torch(model, tokenizer, text, id2rel, device, h_bar=0.3, t_bar=0.3)
```

### 2. Model Architecture Verification
Ensure the model architecture matches exactly:
```python
# Verify in model_torch.py
print(f"Subject head layer: {model.sub_heads_linear}")
print(f"Subject tail layer: {model.sub_tails_linear}")  
print(f"Object head layer: {model.obj_heads_linear}")
print(f"Object tail layer: {model.obj_tails_linear}")
```

### 3. Data Consistency Check
Verify data preprocessing is identical:
```python
# Compare tokenization
original_tokens = keras_bert_tokenizer.tokenize(text)
pytorch_tokens = transformers_tokenizer.tokenize(text)
print(f"Tokenization match: {original_tokens == pytorch_tokens}")
```

## 📊 Performance Monitoring

After applying fixes, monitor these metrics:
- **Recall**: Should increase significantly (target: >85%)
- **Precision**: Should remain stable
- **F1 Score**: Should improve due to better recall

## 🚨 Critical Fix Summary

**The most important fix was removing the early `break` statement in object extraction.** This single line was causing the model to extract only the first object for each subject, which could reduce recall by 50-80% depending on the data.

Run the model again with these fixes and the recall should improve dramatically!
