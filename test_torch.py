#! -*- coding:utf-8 -*-
"""
Test script for PyTorch CasRel implementation
"""
import torch
import os
from transformers import BertTokenizer

# Test imports
try:
    from model_torch import CasRelModel, CasRelLoss
    from data_loader_torch import load_data_torch, create_data_loader
    from utils_torch import get_tokenizer_torch, metric_torch
    print("✓ All imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    exit(1)

def test_model_creation():
    """Test model creation"""
    print("\n=== Testing Model Creation ===")
    
    try:
        # Test with a small BERT model (you might need to download it first)
        bert_model_path = 'bert-base-cased'
        num_rels = 24  # NYT dataset has 24 relations
        
        model = CasRelModel(bert_model_path, num_rels)
        print(f"✓ Model created successfully")
        print(f"  - Hidden size: {model.hidden_size}")
        print(f"  - Number of relations: {model.num_rels}")
        
        # Test forward pass
        batch_size = 2
        seq_len = 50
        
        input_ids = torch.randint(0, 1000, (batch_size, seq_len))
        attention_mask = torch.ones(batch_size, seq_len)
        token_type_ids = torch.zeros(batch_size, seq_len, dtype=torch.long)
        
        # Test subject extraction
        with torch.no_grad():
            sub_heads, sub_tails = model(input_ids, attention_mask, token_type_ids)
            print(f"✓ Subject extraction works - shapes: {sub_heads.shape}, {sub_tails.shape}")
            
            # Test object extraction
            sub_head_idx = torch.tensor([[10], [15]])
            sub_tail_idx = torch.tensor([[12], [18]])
            
            sub_heads, sub_tails, obj_heads, obj_tails = model(
                input_ids, attention_mask, token_type_ids, sub_head_idx, sub_tail_idx
            )
            print(f"✓ Object extraction works - shapes: {obj_heads.shape}, {obj_tails.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False

def test_loss_function():
    """Test loss function"""
    print("\n=== Testing Loss Function ===")
    
    try:
        criterion = CasRelLoss()
        
        batch_size = 2
        seq_len = 50
        num_rels = 24
        
        # Create dummy predictions and targets
        sub_heads_pred = torch.rand(batch_size, seq_len)
        sub_tails_pred = torch.rand(batch_size, seq_len)
        obj_heads_pred = torch.rand(batch_size, seq_len, num_rels)
        obj_tails_pred = torch.rand(batch_size, seq_len, num_rels)
        
        sub_heads_gold = torch.randint(0, 2, (batch_size, seq_len)).float()
        sub_tails_gold = torch.randint(0, 2, (batch_size, seq_len)).float()
        obj_heads_gold = torch.randint(0, 2, (batch_size, seq_len, num_rels)).float()
        obj_tails_gold = torch.randint(0, 2, (batch_size, seq_len, num_rels)).float()
        
        mask = torch.ones(batch_size, seq_len)
        
        total_loss, sub_heads_loss, sub_tails_loss, obj_heads_loss, obj_tails_loss = criterion(
            sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred,
            sub_heads_gold, sub_tails_gold, obj_heads_gold, obj_tails_gold, mask
        )
        
        print(f"✓ Loss calculation works")
        print(f"  - Total loss: {total_loss.item():.4f}")
        print(f"  - Subject head loss: {sub_heads_loss.item():.4f}")
        print(f"  - Subject tail loss: {sub_tails_loss.item():.4f}")
        print(f"  - Object head loss: {obj_heads_loss.item():.4f}")
        print(f"  - Object tail loss: {obj_tails_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Loss function test failed: {e}")
        return False

def test_data_loading():
    """Test data loading"""
    print("\n=== Testing Data Loading ===")
    
    try:
        # Check if NYT data exists
        dataset = 'NYT'
        train_path = f'data/{dataset}/train_triples.json'
        dev_path = f'data/{dataset}/dev_triples.json'
        test_path = f'data/{dataset}/test_triples.json'
        rel_dict_path = f'data/{dataset}/rel2id.json'
        
        if not all(os.path.exists(path) for path in [train_path, dev_path, test_path, rel_dict_path]):
            print("✗ NYT data files not found. Please run build_data.py first.")
            return False
        
        # Load data
        train_data, dev_data, test_data, id2rel, rel2id, num_rels = load_data_torch(
            train_path, dev_path, test_path, rel_dict_path
        )
        
        print(f"✓ Data loaded successfully")
        print(f"  - Train samples: {len(train_data)}")
        print(f"  - Dev samples: {len(dev_data)}")
        print(f"  - Test samples: {len(test_data)}")
        print(f"  - Number of relations: {num_rels}")
        
        # Test tokenizer
        tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
        print(f"✓ Tokenizer loaded")
        
        # Test data loader creation
        train_loader = create_data_loader(
            train_data[:10], tokenizer, rel2id, num_rels, 
            max_len=100, batch_size=2, shuffle=False
        )
        
        # Test one batch
        for batch in train_loader:
            print(f"✓ DataLoader works")
            print(f"  - Batch input_ids shape: {batch['input_ids'].shape}")
            print(f"  - Batch attention_mask shape: {batch['attention_mask'].shape}")
            print(f"  - Batch sub_heads shape: {batch['sub_heads'].shape}")
            print(f"  - Batch obj_heads shape: {batch['obj_heads'].shape}")
            break
        
        return True
        
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=== CasRel PyTorch Implementation Test ===")
    
    # Check PyTorch installation
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name()}")
    
    tests = [
        test_model_creation,
        test_loss_function,
        test_data_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! PyTorch implementation is ready.")
        print("\nTo train the model, run:")
        print("python run_torch.py --train=True --dataset=NYT")
        print("\nTo test the model, run:")
        print("python run_torch.py --train=False --dataset=NYT")
    else:
        print("✗ Some tests failed. Please check the errors above.")

if __name__ == '__main__':
    main()
