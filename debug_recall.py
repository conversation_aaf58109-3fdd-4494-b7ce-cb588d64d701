#! -*- coding:utf-8 -*-
"""
Debug script to identify why recall is low in PyTorch implementation
"""
import torch
import json
import numpy as np
from transformers import BertTokenizer
from model_torch import CasRelModel
from utils_torch import extract_items_torch
from data_loader_torch import load_data_torch

def debug_extraction():
    """Debug the extraction process step by step"""
    
    print("=== Debugging CasRel PyTorch Implementation ===\n")
    
    # Load a small sample of data
    dataset = 'NYT'
    train_path = f'data/{dataset}/train_triples.json'
    dev_path = f'data/{dataset}/dev_triples.json'
    test_path = f'data/{dataset}/test_triples.json'
    rel_dict_path = f'data/{dataset}/rel2id.json'
    
    try:
        train_data, dev_data, test_data, id2rel, rel2id, num_rels = load_data_torch(
            train_path, dev_path, test_path, rel_dict_path
        )
        print(f"✓ Data loaded successfully")
        print(f"  - Number of relations: {num_rels}")
        print(f"  - Dev samples: {len(dev_data)}")
    except Exception as e:
        print(f"✗ Failed to load data: {e}")
        return
    
    # Initialize model and tokenizer
    bert_model_path = 'bert-base-cased'
    try:
        tokenizer = BertTokenizer.from_pretrained(bert_model_path)
        model = CasRelModel(bert_model_path, num_rels, use_span_average=False)  # Use original method
        print(f"✓ Model and tokenizer initialized")
    except Exception as e:
        print(f"✗ Failed to initialize model: {e}")
        return
    
    # Test on a few samples
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)
    model.eval()
    
    print(f"\n=== Testing on sample data ===")
    
    # Take first few samples
    test_samples = dev_data[:5]
    
    for i, sample in enumerate(test_samples):
        print(f"\n--- Sample {i+1} ---")
        text = sample['text']
        gold_triples = sample['triple_list']
        
        print(f"Text: {text}")
        print(f"Gold triples: {gold_triples}")
        
        # Extract using our implementation
        try:
            pred_triples = extract_items_torch(model, tokenizer, text, id2rel, device)
            print(f"Predicted triples: {pred_triples}")
            
            # Calculate recall for this sample
            gold_set = set(gold_triples)
            pred_set = set(pred_triples)
            
            if len(gold_set) > 0:
                recall = len(gold_set & pred_set) / len(gold_set)
                precision = len(gold_set & pred_set) / len(pred_set) if len(pred_set) > 0 else 0
                print(f"Sample recall: {recall:.3f}, precision: {precision:.3f}")
                
                # Show what was missed
                missed = gold_set - pred_set
                if missed:
                    print(f"Missed triples: {missed}")
                
                # Show false positives
                false_pos = pred_set - gold_set
                if false_pos:
                    print(f"False positives: {false_pos}")
            else:
                print("No gold triples for this sample")
                
        except Exception as e:
            print(f"✗ Extraction failed: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n=== Debugging Subject Extraction ===")
    
    # Debug subject extraction specifically
    sample = test_samples[0]
    text = sample['text']
    
    # Tokenize
    encoding = tokenizer(
        text,
        return_tensors='pt',
        max_length=512,
        truncation=True,
        padding=False,
        add_special_tokens=True
    )
    
    input_ids = encoding['input_ids'].to(device)
    attention_mask = encoding['attention_mask'].to(device)
    token_type_ids = encoding.get('token_type_ids', None)
    if token_type_ids is not None:
        token_type_ids = token_type_ids.to(device)
    
    tokens = tokenizer.convert_ids_to_tokens(input_ids[0])
    print(f"Tokens: {tokens}")
    
    with torch.no_grad():
        # Subject extraction
        sub_heads_pred, sub_tails_pred = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids
        )
        
        print(f"Subject head predictions (max 10): {sub_heads_pred[0][:10].cpu().numpy()}")
        print(f"Subject tail predictions (max 10): {sub_tails_pred[0][:10].cpu().numpy()}")
        
        # Check different thresholds
        for threshold in [0.1, 0.3, 0.5, 0.7, 0.9]:
            sub_heads = torch.where(sub_heads_pred[0] > threshold)[0].cpu().numpy()
            sub_tails = torch.where(sub_tails_pred[0] > threshold)[0].cpu().numpy()
            print(f"Threshold {threshold}: {len(sub_heads)} heads, {len(sub_tails)} tails")
            
            if len(sub_heads) > 0 and len(sub_tails) > 0:
                print(f"  Head positions: {sub_heads[:5]}")
                print(f"  Tail positions: {sub_tails[:5]}")
    
    print(f"\n=== Recommendations ===")
    print("1. Check if the model is properly trained")
    print("2. Verify threshold values (try lower thresholds)")
    print("3. Compare with original Keras implementation on same data")
    print("4. Check if BERT tokenization is consistent")
    print("5. Verify data preprocessing pipeline")

if __name__ == '__main__':
    debug_extraction()
