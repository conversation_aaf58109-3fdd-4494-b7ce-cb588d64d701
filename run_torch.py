#! -*- coding:utf-8 -*-
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import os
import argparse
import numpy as np
from tqdm import tqdm

from data_loader_torch import load_data_torch, create_data_loader
from model_torch import CasRelModel, CasRelLoss, EarlyStopping, evaluate_model
from utils_torch import get_tokenizer_torch, metric_torch
from transformers import BertTokenizer

# Set random seeds for reproducibility
torch.manual_seed(2019)
np.random.seed(2019)
if torch.cuda.is_available():
    torch.cuda.manual_seed(2019)

parser = argparse.ArgumentParser(description='CasRel PyTorch Model Controller')
parser.add_argument('--train', default=False, type=bool, help='to train the CasRel model, python run_torch.py --train=True')
parser.add_argument('--dataset', default='NYT', type=str, help='specify the dataset from ["NYT","WebNLG","ACE04","NYT10-HRL","NYT11-HRL","Wiki-KBP"]')
parser.add_argument('--gpu', default='0', type=str, help='GPU device to use')
parser.add_argument('--batch_size', default=6, type=int, help='batch size for training')
parser.add_argument('--epochs', default=100, type=int, help='number of training epochs')
parser.add_argument('--lr', default=1e-5, type=float, help='learning rate')
parser.add_argument('--max_len', default=100, type=int, help='maximum sequence length')
parser.add_argument('--patience', default=7, type=int, help='early stopping patience')
parser.add_argument('--use_span_average', default=True, type=bool, help='use span average for subject features (True) or head+tail average (False)')

args = parser.parse_args()

def train_model(model, train_loader, dev_data, tokenizer, id2rel, device, args, save_path):
    """Train the CasRel model"""
    model.to(device)
    
    # Loss function and optimizer
    criterion = CasRelLoss()
    optimizer = optim.Adam(model.parameters(), lr=args.lr)
    
    # Early stopping
    early_stopping = EarlyStopping(patience=args.patience, save_path=save_path)
    
    model.train()
    best_f1 = 0.0
    
    for epoch in range(args.epochs):
        total_loss = 0.0
        num_batches = 0
        
        # Training loop
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{args.epochs}')
        for batch in progress_bar:
            optimizer.zero_grad()
            
            # Move batch to device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            token_type_ids = batch['token_type_ids'].to(device)
            sub_head_indices = batch['sub_head_indices'].to(device)
            sub_tail_indices = batch['sub_tail_indices'].to(device)
            
            # Gold labels
            sub_heads_gold = batch['sub_heads'].to(device)
            sub_tails_gold = batch['sub_tails'].to(device)
            obj_heads_gold = batch['obj_heads'].to(device)
            obj_tails_gold = batch['obj_tails'].to(device)
            
            # Create mask
            mask = attention_mask.float()
            
            # Forward pass
            sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                token_type_ids=token_type_ids,
                sub_head_idx=sub_head_indices,
                sub_tail_idx=sub_tail_indices
            )
            
            # Calculate loss
            loss, sub_heads_loss, sub_tails_loss, obj_heads_loss, obj_tails_loss = criterion(
                sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred,
                sub_heads_gold, sub_tails_gold, obj_heads_gold, obj_tails_gold, mask
            )
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Avg Loss': f'{total_loss/num_batches:.4f}'
            })
        
        # Evaluation
        print(f"\nEvaluating epoch {epoch+1}...")
        precision, recall, f1 = evaluate_model(model, dev_data, tokenizer, id2rel, device)
        
        print(f'Epoch {epoch+1}: Loss: {total_loss/num_batches:.4f}, '
              f'Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}')
        
        # Early stopping check
        if early_stopping(f1, model, epoch):
            print(f'Early stopping at epoch {epoch+1}')
            break
        
        if f1 > best_f1:
            best_f1 = f1
            print(f'New best F1: {best_f1:.4f}')
        
        model.train()  # Set back to training mode
    
    print(f'Training completed. Best F1: {best_f1:.4f}')


def test_model(model, test_data, tokenizer, id2rel, device, dataset, exact_match=False):
    """Test the trained model"""
    model.eval()
    
    test_result_path = f'results/{dataset}/test_result_torch.json'
    os.makedirs(os.path.dirname(test_result_path), exist_ok=True)
    
    print("Testing model...")
    precision, recall, f1_score = metric_torch(
        model, test_data, id2rel, tokenizer, device, exact_match, test_result_path
    )
    
    print(f'Test Results - Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1_score:.4f}')
    return precision, recall, f1_score


if __name__ == '__main__':
    # Set GPU device
    os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')
    
    # Dataset paths
    dataset = args.dataset
    train_path = f'data/{dataset}/train_triples.json'
    dev_path = f'data/{dataset}/dev_triples.json'
    test_path = f'data/{dataset}/test_triples.json'
    rel_dict_path = f'data/{dataset}/rel2id.json'
    save_weights_path = f'saved_weights/{dataset}/best_model_torch.pt'
    
    # Create save directory
    os.makedirs(os.path.dirname(save_weights_path), exist_ok=True)
    
    # Load data
    print("Loading data...")
    train_data, dev_data, test_data, id2rel, rel2id, num_rels = load_data_torch(
        train_path, dev_path, test_path, rel_dict_path
    )
    
    # Initialize tokenizer
    bert_model_path = 'bert-base-cased'  # You can change this to your local BERT model path
    tokenizer = BertTokenizer.from_pretrained(bert_model_path)
    
    # Initialize model
    print("Initializing model...")
    model = CasRelModel(bert_model_path, num_rels, use_span_average=args.use_span_average)
    print(f"Using {'span average' if args.use_span_average else 'head+tail average'} for subject features")
    
    if args.train:
        print("Starting training...")
        
        # Create data loader
        train_loader = create_data_loader(
            train_data, tokenizer, rel2id, num_rels, 
            args.max_len, args.batch_size, shuffle=True
        )
        
        # Train model
        train_model(model, train_loader, dev_data, tokenizer, id2rel, device, args, save_weights_path)
        
    else:
        # Load trained model
        print(f"Loading model from {save_weights_path}")
        model.load_state_dict(torch.load(save_weights_path, map_location=device))
        model.to(device)
        
        # Test model
        is_exact_match = True if dataset == 'Wiki-KBP' else False
        if is_exact_match:
            print("Using Exact Match evaluation")
        else:
            print("Using Partial Match evaluation")
        
        precision, recall, f1_score = test_model(
            model, test_data, tokenizer, id2rel, device, dataset, is_exact_match
        )
        
        print(f'{precision:.4f}\t{recall:.4f}\t{f1_score:.4f}')
