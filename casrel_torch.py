#! -*- coding:utf-8 -*-
"""
CasRel PyTorch Implementation - Simplified Version
A clean, easy-to-understand implementation of CasRel model
"""
import torch
import torch.nn as nn
import json
import numpy as np
from transformers import BertModel, BertTokenizer
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import argparse
import os
import random

class CasRelModel(nn.Module):
    """Simplified CasRel Model"""
    def __init__(self, bert_model, num_rels):
        super().__init__()
        self.bert = BertModel.from_pretrained(bert_model)
        hidden_size = self.bert.config.hidden_size
        
        # Subject extraction layers
        self.sub_heads = nn.Linear(hidden_size, 1)
        self.sub_tails = nn.Linear(hidden_size, 1)
        
        # Object extraction layers  
        self.obj_heads = nn.Linear(hidden_size, num_rels)
        self.obj_tails = nn.Linear(hidden_size, num_rels)
        
    def forward(self, input_ids, attention_mask, sub_head_idx=None, sub_tail_idx=None):
        # BERT encoding
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        sequence_output = outputs.last_hidden_state  # [batch, seq_len, hidden]
        
        # Subject extraction
        sub_heads_logits = self.sub_heads(sequence_output).squeeze(-1)  # [batch, seq_len]
        sub_tails_logits = self.sub_tails(sequence_output).squeeze(-1)  # [batch, seq_len]
        sub_heads_pred = torch.sigmoid(sub_heads_logits)
        sub_tails_pred = torch.sigmoid(sub_tails_logits)
        
        # Object extraction (only if subject positions provided)
        if sub_head_idx is not None and sub_tail_idx is not None:
            # Get subject features
            batch_size = sequence_output.size(0)
            sub_head_features = sequence_output[torch.arange(batch_size), sub_head_idx.squeeze()]
            sub_tail_features = sequence_output[torch.arange(batch_size), sub_tail_idx.squeeze()]
            sub_features = (sub_head_features + sub_tail_features) / 2  # [batch, hidden]
            
            # Add subject info to all positions
            sub_features = sub_features.unsqueeze(1).expand(-1, sequence_output.size(1), -1)
            enhanced_output = sequence_output + sub_features
            
            # Object prediction
            obj_heads_logits = self.obj_heads(enhanced_output)  # [batch, seq_len, num_rels]
            obj_tails_logits = self.obj_tails(enhanced_output)  # [batch, seq_len, num_rels]
            obj_heads_pred = torch.sigmoid(obj_heads_logits)
            obj_tails_pred = torch.sigmoid(obj_tails_logits)
            
            return sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred
        
        return sub_heads_pred, sub_tails_pred

class CasRelDataset(Dataset):
    """Simplified Dataset"""
    def __init__(self, data, tokenizer, rel2id, max_len=128):
        self.data = data
        self.tokenizer = tokenizer
        self.rel2id = rel2id
        self.max_len = max_len
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = item['text']
        
        # Tokenize
        encoding = self.tokenizer(text, max_length=self.max_len, truncation=True, 
                                padding='max_length', return_tensors='pt')
        
        input_ids = encoding['input_ids'].squeeze()
        attention_mask = encoding['attention_mask'].squeeze()
        tokens = self.tokenizer.convert_ids_to_tokens(input_ids)
        
        # Initialize labels
        seq_len = len(tokens)
        sub_heads = torch.zeros(seq_len)
        sub_tails = torch.zeros(seq_len)
        obj_heads = torch.zeros(seq_len, len(self.rel2id))
        obj_tails = torch.zeros(seq_len, len(self.rel2id))
        
        # Process triples
        subjects = {}
        for triple in item['triple_list']:
            subj_tokens = self.tokenizer.tokenize(triple[0])
            obj_tokens = self.tokenizer.tokenize(triple[2])
            
            # Find positions
            sub_start = self.find_sublist(tokens, subj_tokens)
            obj_start = self.find_sublist(tokens, obj_tokens)
            
            if sub_start != -1 and obj_start != -1:
                sub_end = sub_start + len(subj_tokens) - 1
                obj_end = obj_start + len(obj_tokens) - 1
                rel_id = self.rel2id[triple[1]]
                
                # Subject labels
                sub_heads[sub_start] = 1
                sub_tails[sub_end] = 1
                
                # Store for object labeling
                if (sub_start, sub_end) not in subjects:
                    subjects[(sub_start, sub_end)] = []
                subjects[(sub_start, sub_end)].append((obj_start, obj_end, rel_id))
        
        # Object labels (randomly select one subject like original implementation)
        if subjects:
            sub_pos = random.choice(list(subjects.keys()))  # Random choice like original
            for obj_start, obj_end, rel_id in subjects[sub_pos]:
                obj_heads[obj_start, rel_id] = 1
                obj_tails[obj_end, rel_id] = 1
            sub_head_idx = torch.tensor([sub_pos[0]])
            sub_tail_idx = torch.tensor([sub_pos[1]])
        else:
            sub_head_idx = torch.tensor([0])
            sub_tail_idx = torch.tensor([0])
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'sub_heads': sub_heads,
            'sub_tails': sub_tails,
            'obj_heads': obj_heads,
            'obj_tails': obj_tails,
            'sub_head_idx': sub_head_idx,
            'sub_tail_idx': sub_tail_idx
        }
    
    def find_sublist(self, main_list, sub_list):
        """Find sublist in main list"""
        if not sub_list:
            return -1
        for i in range(len(main_list) - len(sub_list) + 1):
            if main_list[i:i+len(sub_list)] == sub_list:
                return i
        return -1

def load_data(train_path, dev_path, test_path, rel_path):
    """Load and return data"""
    def load_json(path):
        with open(path, 'r', encoding='utf-8') as f:
            return [json.loads(line) for line in f]
    
    with open(rel_path, 'r', encoding='utf-8') as f:
        rel2id = json.load(f)
    
    id2rel = {v: k for k, v in rel2id.items()}
    
    return (load_json(train_path), load_json(dev_path), 
            load_json(test_path), id2rel, rel2id)

def extract_triples(model, tokenizer, text, id2rel, device, threshold=0.5):
    """Extract triples from text"""
    model.eval()
    
    # Tokenize
    encoding = tokenizer(text, return_tensors='pt', max_length=128, truncation=True)
    input_ids = encoding['input_ids'].to(device)
    attention_mask = encoding['attention_mask'].to(device)
    tokens = tokenizer.convert_ids_to_tokens(input_ids[0])
    
    with torch.no_grad():
        # Subject extraction
        sub_heads_pred, sub_tails_pred = model(input_ids, attention_mask)
        
        # Find subjects
        sub_heads = torch.where(sub_heads_pred[0] > threshold)[0].cpu().numpy()
        sub_tails = torch.where(sub_tails_pred[0] > threshold)[0].cpu().numpy()
        
        subjects = []
        for h in sub_heads:
            for t in sub_tails:
                if h <= t:
                    subjects.append((h, t))
                    break
        
        # Extract objects for each subject
        triples = []
        for sub_head, sub_tail in subjects:
            sub_head_idx = torch.tensor([[sub_head]], device=device)
            sub_tail_idx = torch.tensor([[sub_tail]], device=device)
            
            _, _, obj_heads_pred, obj_tails_pred = model(
                input_ids, attention_mask, sub_head_idx, sub_tail_idx)
            
            # Find objects
            obj_heads = torch.where(obj_heads_pred[0] > threshold)
            obj_tails = torch.where(obj_tails_pred[0] > threshold)
            
            for oh, or_h in zip(obj_heads[0].cpu(), obj_heads[1].cpu()):
                for ot, or_t in zip(obj_tails[0].cpu(), obj_tails[1].cpu()):
                    if oh <= ot and or_h == or_t:
                        # Extract text
                        sub_text = tokenizer.convert_tokens_to_string(tokens[sub_head:sub_tail+1])
                        obj_text = tokenizer.convert_tokens_to_string(tokens[oh:ot+1])
                        rel = id2rel[or_h.item()]
                        
                        # Clean text
                        sub_text = sub_text.replace(' ##', '').replace('##', '')
                        obj_text = obj_text.replace(' ##', '').replace('##', '')
                        
                        triples.append((sub_text, rel, obj_text))
                        break
    
    return list(set(triples))  # Remove duplicates

def calculate_metrics(pred_triples, gold_triples):
    """Calculate precision, recall, F1"""
    pred_set = set(pred_triples)
    gold_set = set(gold_triples)

    if len(pred_set) == 0:
        precision = 0
    else:
        precision = len(pred_set & gold_set) / len(pred_set)

    if len(gold_set) == 0:
        recall = 0
    else:
        recall = len(pred_set & gold_set) / len(gold_set)

    if precision + recall == 0:
        f1 = 0
    else:
        f1 = 2 * precision * recall / (precision + recall)

    return precision, recall, f1

def train_model(model, train_loader, dev_data, tokenizer, id2rel, device, epochs=10):
    """Training function"""
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-5)
    criterion = nn.BCELoss()

    best_f1 = 0
    for epoch in range(epochs):
        total_loss = 0
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs}')

        for batch in progress_bar:
            optimizer.zero_grad()

            # Move to device
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            sub_heads_gold = batch['sub_heads'].to(device)
            sub_tails_gold = batch['sub_tails'].to(device)
            obj_heads_gold = batch['obj_heads'].to(device)
            obj_tails_gold = batch['obj_tails'].to(device)
            sub_head_idx = batch['sub_head_idx'].to(device)
            sub_tail_idx = batch['sub_tail_idx'].to(device)

            # Forward pass
            sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred = model(
                input_ids, attention_mask, sub_head_idx, sub_tail_idx)

            # Calculate loss
            mask = attention_mask.float()

            sub_heads_loss = criterion(sub_heads_pred, sub_heads_gold)
            sub_heads_loss = (sub_heads_loss * mask).sum() / mask.sum()

            sub_tails_loss = criterion(sub_tails_pred, sub_tails_gold)
            sub_tails_loss = (sub_tails_loss * mask).sum() / mask.sum()

            obj_heads_loss = criterion(obj_heads_pred, obj_heads_gold)
            obj_heads_loss = (obj_heads_loss.sum(dim=2) * mask).sum() / mask.sum()

            obj_tails_loss = criterion(obj_tails_pred, obj_tails_gold)
            obj_tails_loss = (obj_tails_loss.sum(dim=2) * mask).sum() / mask.sum()

            loss = sub_heads_loss + sub_tails_loss + obj_heads_loss + obj_tails_loss

            # Backward pass
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})

        # Evaluation
        if (epoch + 1) % 2 == 0:  # Evaluate every 2 epochs
            precision, recall, f1 = evaluate_model(model, dev_data, tokenizer, id2rel, device)
            print(f'Epoch {epoch+1}: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}')

            if f1 > best_f1:
                best_f1 = f1
                torch.save(model.state_dict(), 'best_model.pt')
                print(f'New best F1: {f1:.3f}')

def evaluate_model(model, test_data, tokenizer, id2rel, device):
    """Evaluation function"""
    model.eval()
    all_pred_triples = []
    all_gold_triples = []

    for item in tqdm(test_data[:100], desc='Evaluating'):  # Evaluate on subset
        text = item['text']
        gold_triples = [tuple(triple) for triple in item['triple_list']]
        pred_triples = extract_triples(model, tokenizer, text, id2rel, device)

        all_pred_triples.extend(pred_triples)
        all_gold_triples.extend(gold_triples)

    return calculate_metrics(all_pred_triples, all_gold_triples)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='CasRel PyTorch - Simplified')
    parser.add_argument('--dataset', default='NYT', help='Dataset name')
    parser.add_argument('--batch_size', default=16, type=int, help='Batch size')
    parser.add_argument('--epochs', default=10, type=int, help='Training epochs')
    parser.add_argument('--train', action='store_true', help='Train model')
    args = parser.parse_args()

    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    # Load data
    dataset = args.dataset
    train_data, dev_data, test_data, id2rel, rel2id = load_data(
        f'data/{dataset}/train_triples.json',
        f'data/{dataset}/dev_triples.json',
        f'data/{dataset}/test_triples.json',
        f'data/{dataset}/rel2id.json'
    )

    print(f'Loaded {len(train_data)} train, {len(dev_data)} dev, {len(test_data)} test samples')
    print(f'Number of relations: {len(rel2id)}')

    # Initialize model
    tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
    model = CasRelModel('bert-base-cased', len(rel2id)).to(device)

    if args.train:
        # Training
        train_dataset = CasRelDataset(train_data, tokenizer, rel2id)
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)

        print('Starting training...')
        train_model(model, train_loader, dev_data, tokenizer, id2rel, device, args.epochs)
    else:
        # Load trained model
        if os.path.exists('best_model.pt'):
            model.load_state_dict(torch.load('best_model.pt', map_location=device))
            print('Loaded trained model')
        else:
            print('No trained model found. Please train first with --train')
            return

    # Final evaluation
    precision, recall, f1 = evaluate_model(model, test_data, tokenizer, id2rel, device)
    print(f'Final Results: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}')

if __name__ == '__main__':
    main()
